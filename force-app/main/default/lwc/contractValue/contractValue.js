/* eslint-disable */
import { LightningElement, api } from "lwc";

export default class ContractValue extends LightningElement {
  @api transactions = [];
  @api payApplications = [];
  @api projectData = [];

  connectedCallback() {
    console.log(
      "API – payApplications:",
      JSON.stringify(this.payApplications)
    );
    console.log("API – transactions:", JSON.stringify(this.transactions));
    console.log("API – projectData:", JSON.stringify(this.projectData));
  }

  /**
   * Given a JS Date, return the Date object corresponding to that week’s Friday.
   * If the date is already a Friday (getDay() === 5), return it (with hours zeroed).
   * Otherwise, move forward to the next Friday.
   */
  getNextFriday(date) {
    const d = new Date(date);
    const dow = d.getDay(); // 0=Sun,1=Mon,…,5=Fri,6=Sat
    const offset = (5 - dow + 7) % 7;
    d.setDate(d.getDate() + offset);
    d.setHours(0, 0, 0, 0);
    return d;
  }

  /**
   * Helper: format an ISO datetime string into MM/DD/YYYY
   */
  formatDate(isoString) {
    if (!isoString) return "";
    const dt = new Date(isoString);
    const year = dt.getFullYear();
    const month = `0${dt.getMonth() + 1}`.slice(-2);
    const day = `0${dt.getDate()}`.slice(-2);
    return `${month}/${day}/${year}`;
  }

  /**
   * Build a row per Friday‐bucket:
   * 1) Compute initialBeg = Remaining_Contract_Value__c * (1 – RetainageFrac)
   * 2) For each transaction:
   *      • Find its related PayApplication (via Related_Pay_Application__c)
   *      • Compute grossPayApp = projectedPayment * (1 − retainageFrac)
   *      • Assign weekKey = getNextFriday(CreatedDate)
   * 3) Group all transactions by weekKey, summing grossPayApp per bucket
   * 4) Sort the buckets by weekDate asc. Then:
   *      Week 1: beg = initialBeg; payAppSum = that bucket’s sum; ending = beg − payAppSum
   *      Week n: beg = previous ending; payAppSum = this bucket’s sum; ending = beg − payAppSum
   * 5) Return rows = [{ id, date, week, beg, payApp, ending }, …]
   */
  get displayRows() {
    // 1) Normalize inputs to arrays
    const payApps = Array.isArray(this.payApplications)
      ? this.payApplications
      : this.payApplications
      ? [this.payApplications]
      : [];

    const txns = Array.isArray(this.transactions)
      ? this.transactions
      : this.transactions
      ? [this.transactions]
      : [];

    const projArr = Array.isArray(this.projectData)
      ? this.projectData
      : this.projectData
      ? [this.projectData]
      : [];

    // 2) Grab the one project record (assuming they all belong to the same project)
    const project = projArr.length > 0 ? projArr[0] : {};
    const rawRemain =
      typeof project.Remaining_Contract_Value__c === "number"
        ? project.Remaining_Contract_Value__c
        : 0;
    const rawRetainage =
      typeof project.Retainage__c === "number" ? project.Retainage__c : 0;

    console.log(`Project ID: ${project.Id}`);
    console.log(`  Remaining_Contract_Value__c = ${rawRemain}`);
    console.log(`  Retainage__c (raw percent)  = ${rawRetainage}`);

    const retainageFrac = rawRetainage / 100; // e.g. 5% → 0.05

    // 3) Compute the initial “beg” before any transactions happen
    let initialBeg = rawRemain * (1 - retainageFrac);
    console.log(`Initial Beg = ${rawRemain} * (1 - ${retainageFrac}) = ${initialBeg}`);

    // 4) Sort all transactions by CreatedDate (oldest→newest)
    const sortedTxns = txns.slice().sort((a, b) => {
      return new Date(a.CreatedDate).getTime() - new Date(b.CreatedDate).getTime();
    });

    // 5) Build per‐transaction objects with { weekKey, grossPayApp }
    const perTxn = sortedTxns.map((tx) => {
      const txDate = new Date(tx.CreatedDate);

      // a) Compute grossPayApp for this transaction
      let grossPayApp = 0;
      const payAppId = tx.Related_Pay_Application__c;
      if (payAppId) {
        const payAppRec = payApps.find((pa) => pa.Id === payAppId);
        if (payAppRec) {
          const projectedPayment =
            typeof payAppRec.Projected_payment__c === "number"
              ? payAppRec.Projected_payment__c
              : 0;
          // grossPayApp = projectedPayment * (1 − retainageFrac)
          grossPayApp = projectedPayment * (1 - retainageFrac);
          console.log(
            `  Txn ${tx.Id} | Projected_payment__c = ${projectedPayment}`
          );
          console.log(
            `    → Computed grossPayApp = ${projectedPayment} * (1 - ${retainageFrac}) = ${grossPayApp}`
          );
        } else {
          console.log(
            `  Txn ${tx.Id} | No PayApp record found for ID ${payAppId}; grossPayApp=0`
          );
        }
      } else {
        console.log(
          `  Txn ${tx.Id} | No Related_Pay_Application__c; grossPayApp=0`
        );
      }

      // b) Determine which Friday this transaction belongs to
      const weekKey = this.getNextFriday(txDate);

      return {
        weekKey,
        grossPayApp,
      };
    });

    // 6) Group perTxn by weekKey timestamp, summing grossPayApp
    const bucketMap = perTxn.reduce((acc, obj) => {
      const key = obj.weekKey.getTime();
      if (!acc[key]) {
        acc[key] = {
          weekDate: obj.weekKey,
          grossSum: 0,
        };
      }
      acc[key].grossSum += obj.grossPayApp;
      return acc;
    }, {});

    // 7) Convert bucketMap into an array and sort by weekDate ascending
    const sortedBuckets = Object.values(bucketMap).sort((a, b) => {
      return a.weekDate.getTime() - b.weekDate.getTime();
    });

    // 8) Build final rows with “carry forward” logic
    let weekCounter = 1;
    let prevEnding = initialBeg; // start with initialBeg as “previous ending” for our loop
    return sortedBuckets.map((grp) => {
      const beg = prevEnding;
      const payAppSum = grp.grossSum;
      const ending = beg - payAppSum;

      const row = {
        id: grp.weekDate.getTime().toString(),
        date: grp.weekDate.toLocaleDateString(),
        week: weekCounter,
        beg: `$ ${beg.toFixed(2)}`,
        payApp: `$ ${payAppSum.toFixed(2)}`,
        ending: `$ ${ending.toFixed(2)}`,
      };

      // Prepare for next iteration
      prevEnding = ending;
      weekCounter += 1;
      return row;
    });
  }
}