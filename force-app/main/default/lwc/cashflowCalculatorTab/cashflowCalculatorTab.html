<template>
    <div class="slds-p-around_medium">
        <template if:true={calculatorData}>

            <div class="slds-section slds-is-open">
                <h3 class="slds-section__title slds-theme_shade">
                    <span class="slds-truncate slds-p-horizontal_small" title="Loan Summary">Loan Summary</span>
                </h3>
                <div class="slds-section__content slds-p-around_small slds-grid slds-wrap slds-grid_align-spread">


                    <div class="slds-col slds-size_1-of-2 slds-p-bottom_small">
                        <dl class="slds-list_horizontal slds-wrap">
                            <dt class="slds-item_label slds-text-color_weak slds-truncate slds-size_1-of-2"
                                title="MF Cash Out">MF Cash Out:</dt>
                            <dd class="slds-item_detail slds-truncate slds-size_1-of-2 slds-m-bottom_x-small">
                                <lightning-formatted-number value={mfCashOut}
                                    format-style="currency" currency-code="USD"></lightning-formatted-number>
                            </dd>

                            <dt class="slds-item_label slds-text-color_weak slds-truncate slds-size_1-of-2"
                                title="Origination Fee">Origination Fee:</dt>
                            <dd class="slds-item_detail slds-truncate slds-size_1-of-2 slds-m-bottom_x-small">
                                <lightning-formatted-number value={originationFee}
                                    format-style="currency" currency-code="USD"></lightning-formatted-number>
                            </dd>

                            <dt class="slds-item_label slds-text-color_weak slds-truncate slds-size_1-of-2"
                                title="Interest Income">Interest Income:</dt>
                            <dd class="slds-item_detail slds-truncate slds-size_1-of-2 slds-m-bottom_x-small">
                                <lightning-formatted-number value={interestIncome}
                                    format-style="currency" currency-code="USD"></lightning-formatted-number>
                            </dd>

                            <dt class="slds-item_label slds-text-color_weak slds-truncate slds-size_1-of-2"
                                title="Total Income">Total Income:</dt>
                            <dd class="slds-item_detail slds-truncate slds-size_1-of-2 slds-m-bottom_x-small">
                                <lightning-formatted-number value={totalIncome}
                                    format-style="currency" currency-code="USD"></lightning-formatted-number>
                            </dd>
                        </dl>
                    </div>


                    <div class="slds-col slds-size_1-of-2 slds-p-bottom_small">
                        <dl class="slds-list_horizontal slds-wrap">
                            <dt class="slds-item_label slds-text-color_weak slds-truncate slds-size_1-of-2"
                                title="Weeks Outstanding">Weeks Outstanding:</dt>
                            <dd class="slds-item_detail slds-truncate slds-size_1-of-2 slds-m-bottom_x-small">
                                {weeksOutstanding}
                            </dd>

                            <dt class="slds-item_label slds-text-color_weak slds-truncate slds-size_1-of-2"
                                title="Total Principal + Interest">Total Principal + Interest:</dt>
                            <dd class="slds-item_detail slds-truncate slds-size_1-of-2 slds-m-bottom_x-small">
                                <lightning-formatted-number value={totalPrincipalInterest}
                                    format-style="currency" currency-code="USD"></lightning-formatted-number>
                            </dd>

                            <dt class="slds-item_label slds-text-color_weak slds-truncate slds-size_1-of-2"
                                title="Revolver Availability">Revolver Availability:</dt>
                            <dd class="slds-item_detail slds-truncate slds-size_1-of-2 slds-m-bottom_x-small">
                                <lightning-formatted-number value={revolverAvailability}
                                    format-style="currency" currency-code="USD"></lightning-formatted-number>
                            </dd>
                        </dl>
                    </div>

                </div>
            </div>


           


            <lightning-tabset onactive={handleTabActive} active-tab-value={activeTab}>
                <lightning-tab label="Loan Disbursements" value="loanDisbursements">
                    <c-loan-disbursement week-columns={weekColumns} cash-flow-data={cashFlowData} transactions={transactions} disbursements={disbursements}
                        rows={calculatorData.loanDetails} oncalculatedit={handleChildEdit}></c-loan-disbursement>
                </lightning-tab>
                <lightning-tab label="Interest Accrual" value="interestAccrual">
                    <c-interest-accrual week-columns={weekColumns} cash-flow-data={cashFlowData} transactions={transactions} disbursements={disbursements}
                        rows={calculatorData.loanDetails} oncalculatedit={handleChildEdit}></c-interest-accrual>
                </lightning-tab>
                <lightning-tab label="Pay Application" value="PayApplication">
                    <c-pay-application week-columns={weekColumns} cash-flow-data={cashFlowData} pay-applications={payApplications} transactions={transactions} project-data={projectData}
                        rows={calculatorData.loanDetails} oncalculatedit={handleChildEdit}></c-pay-application>
                </lightning-tab>
                <lightning-tab label="Contract Value" value="ContractValue">
                    <c-contract-value week-columns={weekColumns} cash-flow-data={cashFlowData} pay-applications={payApplications} transactions={transactions} 
                       project-data={projectData} rows={calculatorData.loanDetails} oncalculatedit={handleChildEdit}></c-contract-value>
                </lightning-tab>
                <lightning-tab label="Outstanding Less Orig Fees" value="OutstandingLessOrigFees">
                    <c-outstanding-less-orig-fees week-columns={weekColumns} cash-flow-data={cashFlowData} transactions={transactions} disbursements={disbursements}
                        rows={calculatorData.loanDetails} oncalculatedit={handleChildEdit}></c-outstanding-less-orig-fees>
                </lightning-tab>
            </lightning-tabset>


        </template>
        <template if:false={calculatorData}>
            <p>No calculator data available.</p>
        </template>
    </div>
</template>