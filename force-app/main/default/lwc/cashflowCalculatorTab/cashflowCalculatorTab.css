.slds-section__title {
    background-color: #f3f3f3;
    padding: var(--slds-spacing-x-small) var(--slds-spacing-small);
    border: 1px solid #dddbda;
    border-bottom: none;
}
.slds-section__content {
    border: 1px solid #dddbda;
}
.slds-table th {
    background-color: #fafafa;
}
.slds-table td lightning-input {
    margin-bottom: 0; /* Remove default margin from input in table cell */
}
.slds-scrollable_x {
    overflow-x: auto;
}

:host {
  /* active-tab bottom border */
  --slds-c-tabs-item-color-border-active: #44A569;
  /* hover state (optional) */
  --slds-c-tabs-item-color-border-hover: #365ada;
  /* the whole tab bar’s bottom border (optional) */
  /* --slds-c-tabs-list-color-border: #44A569; */
}