/* eslint-disable */
import { LightningElement, api } from "lwc";

export default class PayApplication extends LightningElement {
  /** Raw lists passed in from parent; could be arrays or single objects. */
  @api payApplications = [];
  @api transactions = [];
  @api projectData = [];
  @api cashFlowData = [];

  /** Debug: log raw inputs and generated JSON */
  connectedCallback() {
    console.log("API  payApplications:", JSON.stringify(this.payApplications));
    console.log("API  transactions:", JSON.stringify(this.transactions));
    console.log("API  projectData:", JSON.stringify(this.projectData));
    console.log("API  cashFlowData:", JSON.stringify(this.cashFlowData));

    // Log the generated JSON for PayApplication
    console.log("Generated PayApplication JSON:", JSON.stringify(this.calculatorJson, null, 2));
  }

  /**
   * Helper: format an ISO datetime string into MM/DD/YYYY
   */
  formatDate(isoString) {
    if (!isoString) {
      return "";
    }
    const dt = new Date(isoString);
    const year = dt.getFullYear();
    const month = `0${dt.getMonth() + 1}`.slice(-2);
    const day = `0${dt.getDate()}`.slice(-2);
    return `${month}/${day}/${year}`;
  }

  /**
   * Given a JS Date, return the Date object for the Friday of that week.
   * - If it's already Friday (getDay()===5), return that date (with hours zeroed).
   * - Otherwise, move forward to the next Friday.
   *
   * Note: JS getDay(): Sunday=0, Monday=1, … Friday=5, Saturday=6.
   */
  getNextFriday(date) {
    const d = new Date(date);
    const dow = d.getDay();
    const offset = (5 - dow + 7) % 7;
    d.setDate(d.getDate() + offset);
    d.setHours(0, 0, 0, 0);
    return d;
  }

  /**
   * Build “weekly‐aggregated” rows for the Pay Application tab.
   */
  get displayRows() {
    // 1) Normalize inputs to arrays
    const payApps = Array.isArray(this.payApplications)
      ? this.payApplications
      : this.payApplications
      ? [this.payApplications]
      : [];

    const txns = Array.isArray(this.transactions)
      ? this.transactions
      : this.transactions
      ? [this.transactions]
      : [];

    const projArray = Array.isArray(this.projectData)
      ? this.projectData
      : this.projectData
      ? [this.projectData]
      : [];

    // 2) Sort all transactions by CreatedDate ascending
    const sortedTxns = txns
      .slice()
      .sort(
        (a, b) =>
          new Date(a.CreatedDate).getTime() - new Date(b.CreatedDate).getTime()
      );

    // 3) Build per‐txn objects containing { weekKey, grossPayApp, appliedToMfLoan }
    const perTxn = sortedTxns.map((tx) => {
      // 3a) Attempt to find the related Pay Application record
      const payAppId = tx.Related_Pay_Application__c;
      let projectedPayment = 0;
      let rawPercentToLoan = 0;
      if (payAppId) {
        const payApp = payApps.find((pa) => pa.Id === payAppId);
        if (payApp) {
          projectedPayment =
            typeof payApp.Projected_payment__c === "number"
              ? payApp.Projected_payment__c
              : 0;
          rawPercentToLoan =
            typeof payApp.of_Pay_App_to_Loan__c === "number"
              ? payApp.of_Pay_App_to_Loan__c
              : 0;
        }
      }

      // 3b) Find the related Project to grab rawRetainage
      const projectId = tx.Project__c;
      const proj = projArray.find((p) => p.Id === projectId) || {};
      const rawRetainage =
        typeof proj.Retainage__c === "number" ? proj.Retainage__c : 0;

      // 3c) Compute fractions & values
      const retainageFrac = rawRetainage / 100; // e.g. 5% → 0.05
      const percentFrac = rawPercentToLoan / 100; // e.g. 20% → 0.20

      // 3d) Gross Pay Application after retainage
      const grossPayApp = projectedPayment * (1 - retainageFrac);

      // 3e) Applied to MF Loan = grossPayApp * percentFrac
      const appliedToMfLoan = grossPayApp * percentFrac;

      // 3f) Compute weekKey = next (or same) Friday of this transaction’s CreatedDate
      const weekKey = this.getNextFriday(new Date(tx.CreatedDate));

      return {
        weekKey,
        grossPayApp,
        appliedToMfLoan,
      };
    });

    // 4) Group per‐txn objects by weekKey timestamp, summing gross & applied
    const weeklyMap = perTxn.reduce((acc, obj) => {
      const key = obj.weekKey.getTime();
      if (!acc[key]) {
        acc[key] = {
          weekDate: obj.weekKey,
          grossSum: 0,
          appliedSum: 0,
        };
      }
      acc[key].grossSum += obj.grossPayApp;
      acc[key].appliedSum += obj.appliedToMfLoan;
      return acc;
    }, {});

    // 5) Convert map to an array of { weekDate, grossSum, appliedSum }, then sort by weekDate
    const sortedWeeks = Object.values(weeklyMap).sort((a, b) => {
      return a.weekDate.getTime() - b.weekDate.getTime();
    });

    // 6) Build final rows, assigning week = 1, 2, 3…
    let weekCounter = 1;
    return sortedWeeks.map((grp) => {
      const gross = grp.grossSum;
      const applied = grp.appliedSum;
      // Weighted average percent = (appliedSum / grossSum) * 100
      const percentToMfLoan =
        gross > 0 ? (applied / gross) * 100 : 0;

      return {
        id: grp.weekDate.getTime().toString(),
        date: grp.weekDate.toLocaleDateString(),
        week: weekCounter++,
        grossPayApp: `$ ${gross.toFixed(2)}`,
        percentToMfLoan: `${percentToMfLoan.toFixed(2)}%`,
        appliedToMfLoan: `$ ${applied.toFixed(2)}`,
      };
    });
  }

  /**
   * Build the JSON structure for only the PayApplication tab:
   * {
   *   cashflowCalculatorData: [
   *     {
   *       id: "1",
   *       tabName: "PayApplication",
   *       weeks: [ … output of displayRows … ]
   *     }
   *   ]
   * }
   */
  get calculatorJson() {
    const payAppWeeks = this.displayRows;
    return {
      cashflowPayAppCalculatorData: [
        {
          id: "1",
          tabName: "PayApplication",
          weeks: payAppWeeks,
        },
      ],
    };
  }

  /**
   * Expose a ready‐made JSON string if needed:
   *   JSON.stringify(this.calculatorJson, null, 2)
   */
  get calculatorJsonString() {
    return JSON.stringify(this.calculatorJson, null, 2);
  }
}