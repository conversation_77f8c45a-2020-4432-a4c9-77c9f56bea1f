<template>
  <div class="slds-scrollable_x">
    <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-table_fixed-layout">
      <thead>
        <tr>
          <th scope="col">
            <div class="slds-truncate slds-text-align_center" title="Date">Date</div>
          </th>
          <th scope="col">
            <div class="slds-truncate slds-text-align_center" title="Week">Week</div>
          </th>
          <th scope="col">
            <div class="slds-truncate slds-text-align_center" title="Beg">Beg</div>
          </th>
          <th scope="col">
            <div class="slds-truncate slds-text-align_center" title="Loan Disb">Loan <PERSON>sb</div>
          </th>
          <th scope="col">
            <div class="slds-truncate slds-text-align_center" title="Orig Fees">Orig Fees</div>
          </th>
          <th scope="col">
            <div class="slds-truncate slds-text-align_center" title="Payment">Payment</div>
          </th>
          <th scope="col">
            <div class="slds-truncate slds-text-align_center" title="Outstanding">Outstanding</div>
          </th>
        </tr>
      </thead>
      <tbody>
        <!-- one <tr> per weekColumn -->
        <template for:each={displayRows} for:item="row">
          <tr key={row.id}>
            <td class="slds-text-align_center">{row.date}</td>
            <td class="slds-text-align_center">{row.week}</td>
            <td class="slds-text-align_center">{row.beg}</td>
            <td class="slds-text-align_center">{row.loanDisb}</td>
            <td class="slds-text-align_center">{row.origFees}</td>
            <td class="slds-text-align_center">{row.payment}</td>
            <td class="slds-text-align_center">{row.outstanding}</td>
          </tr>
        </template>
      </tbody>
    </table>
  </div>
</template>