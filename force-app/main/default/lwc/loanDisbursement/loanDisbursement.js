/* eslint-disable */

import { LightningElement, api } from "lwc";

export default class LoanDisbursement extends LightningElement {
  @api transactions = [];
  @api disbursements = [];
  @api cashFlowData = [];

  connectedCallback() {
    
    console.log("API  transactions:", JSON.stringify(this.transactions));
    
    console.log("API  cashFlowData:", JSON.stringify(this.cashFlowData));

    
  }

  get projectId() {
    return Array.isArray(this.disbursements) && this.disbursements.length
      ? this.disbursements[0].Project__c
      : null;
  }

  /**
   * Given a JS Date, return the Date object for the Friday of that week.
   * If date is already a Friday (getDay() === 5), return it (with hours zeroed).
   * Otherwise, “push forward” to the next Friday.
   */
  getNextFriday(date) {
    const d = new Date(date);
    const dow = d.getDay(); // Sunday=0, Monday=1, …, Friday=5, Saturday=6
    const offset = (5 - dow + 7) % 7; 
    d.setDate(d.getDate() + offset);
    d.setHours(0, 0, 0, 0);
    return d;
  }

  get displayRows() {
    // 1) Filter + sort all transactions for this project
    const txns = (Array.isArray(this.transactions) ? this.transactions : [])
      .filter((tx) => tx.Project__c === this.projectId)
      .sort(
        (a, b) =>
          new Date(a.CreatedDate).getTime() - new Date(b.CreatedDate).getTime()
      );

    // 2) Build an array of { weekKey, loanDisb, origFees, payment, outstanding }
    let prevOutstanding = 0;
    const perTxn = txns.map((tx) => {
      const txDate = new Date(tx.CreatedDate);

      const loanDisb = tx.Loan_Principal_to_date__c || 0;
      const origFees =
        (tx.Default_Fee_Application__c || 0) +
        (tx.Doc_Stamp_Fees_Application__c || 0) +
        (tx.Late_Fee_Application__c || 0) +
        (tx.Legal_Fees_Application__c || 0);
      const payment = tx.Amount__c || 0;
      const outstanding = loanDisb + origFees - payment;

      const weekKey = this.getNextFriday(txDate);

      // Update prevOutstanding for next iteration
      prevOutstanding = outstanding;

      return {
        weekKey,
        loanDisb,
        origFees,
        payment,
        outstanding,
      };
    });

    // 3) Group perTxn by weekKey (Friday timestamp), summing loanDisb, origFees, payment, outstanding
    const groups = perTxn.reduce((acc, obj) => {
      const key = obj.weekKey.getTime();
      if (!acc[key]) {
        acc[key] = {
          weekDate: obj.weekKey,
          loanDisbSum: 0,
          origFeesSum: 0,
          paymentSum: 0,
          outstandingSum: 0,
        };
      }
      acc[key].loanDisbSum += obj.loanDisb;
      acc[key].origFeesSum += obj.origFees;
      acc[key].paymentSum += obj.payment;
      acc[key].outstandingSum += obj.outstanding;
      return acc;
    }, {});

    // 4) Convert groups into a sorted array by weekDate
    const sortedFridays = Object.values(groups).sort((a, b) => {
      return a.weekDate.getTime() - b.weekDate.getTime();
    });

    // 5) Build final rows, setting beg = previous row’s outstandingSum
    let weekCount = 1;
    let prevWeekOutstanding = 0;
    return sortedFridays.map((grp) => {
      // For Week 1, beg = 0. Otherwise, carry forward prevWeekOutstanding.
      const beg = weekCount === 1 ? 0 : prevWeekOutstanding;
      const outstanding = grp.outstandingSum;

      const row = {
        id: grp.weekDate.getTime().toString(),
        date: grp.weekDate.toLocaleDateString(),
        week: weekCount,
        beg: `$ ${beg.toFixed(2)}`,
        loanDisb: `$ ${grp.loanDisbSum.toFixed(2)}`,
        origFees: `$ ${grp.origFeesSum.toFixed(2)}`,
        payment: `$ ${grp.paymentSum.toFixed(2)}`,
        outstanding: `$ ${outstanding.toFixed(2)}`,
      };

      prevWeekOutstanding = outstanding;
      weekCount += 1;
      return row;
    });
  }
}