/* eslint-disable */
import { LightningElement, api } from "lwc";

export default class OutstandingLessOrigFees extends LightningElement {
  /** data passed in */
  @api transactions = [];
  @api disbursements = [];

  /** grab projectId from first disbursement */
  get projectId() {
    return Array.isArray(this.disbursements) && this.disbursements.length
      ? this.disbursements[0].Project__c
      : null;
  }

  /**
   * Given a JS Date, return the Date object corresponding to that week’s Friday.
   * If the date is already Friday (getDay() === 5), return it (with hours zeroed).
   * Otherwise, move forward to the next Friday.
   */
  getNextFriday(date) {
    const d = new Date(date);
    const dow = d.getDay(); // Sunday=0, Monday=1, …, Friday=5, Saturday=6
    const offset = (5 - dow + 7) % 7;
    d.setDate(d.getDate() + offset);
    d.setHours(0, 0, 0, 0);
    return d;
  }

  /** build the rows: one row per week (Friday) with summed outstandingLessOrigFees */
  get displayRows() {
    // 1) Filter + sort all transactions for this project
    const txns = (Array.isArray(this.transactions) ? this.transactions : [])
      .filter((tx) => tx.Project__c === this.projectId)
      .sort(
        (a, b) =>
          new Date(a.CreatedDate).getTime() - new Date(b.CreatedDate).getTime()
      );

    // 2) Build per‐transaction list of { weekKey, outstandingLessOrigFees }
    const perTxn = txns.map((tx) => {
      // a) Compute loanDisb, origFees, payment, outstanding
      const loanDisb = tx.Loan_Principal_to_date__c || 0;
      const origFees =
        (tx.Default_Fee_Application__c || 0) +
        (tx.Doc_Stamp_Fees_Application__c || 0) +
        (tx.Late_Fee_Application__c || 0) +
        (tx.Legal_Fees_Application__c || 0);
      const payment = tx.Amount__c || 0;
      const outstanding = loanDisb + origFees - payment;

      // b) Compute outstandingLessOrigFees (as per your original code)
      const outstandingLessOrigFees = outstanding - payment;

      // c) Determine this transaction’s Friday (weekKey)
      const weekKey = this.getNextFriday(new Date(tx.CreatedDate));

      return {
        weekKey,
        outstandingLessOrigFees,
      };
    });

    // 3) Group perTxn entries by weekKey timestamp, summing outstandingLessOrigFees
    const groupMap = perTxn.reduce((acc, obj) => {
      const key = obj.weekKey.getTime();
      if (!acc[key]) {
        acc[key] = {
          weekDate: obj.weekKey,
          sumValue: 0,
        };
      }
      acc[key].sumValue += obj.outstandingLessOrigFees;
      return acc;
    }, {});

    // 4) Convert groups into an array and sort by weekDate ascending
    const sortedWeeks = Object.values(groupMap).sort((a, b) => {
      return a.weekDate.getTime() - b.weekDate.getTime();
    });

    // 5) Build final rows with a “week” counter
    let weekCounter = 1;
    return sortedWeeks.map((grp) => {
      return {
        id: grp.weekDate.getTime().toString(),
        date: grp.weekDate.toLocaleDateString(),
        week: weekCounter++,
        value: `$ ${grp.sumValue.toFixed(2)}`,
      };
    });
  }
}