<template>
  <div class="slds-scrollable_x">
    <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-table_fixed-layout">
      <thead>
        <tr>
          <th scope="col">
            <div class="slds-truncate slds-text-align_center" title="Date">Date</div>
          </th>
          <th scope="col">
            <div class="slds-truncate slds-text-align_center" title="Week">Beg Balance</div>
          </th>
          <th scope="col">
            <div class="slds-truncate slds-text-align_center" title="Beg">Accrual</div>
          </th>
          <th scope="col">
            <div class="slds-truncate slds-text-align_center" title="Loan Disb">Payment</div>
          </th>
          <th scope="col">
            <div class="slds-truncate slds-text-align_center" title="Orig Fees">Outstanding Int</div>
          </th>

        </tr>
      </thead>
      <tbody>
        <!-- one <tr> per weekColumn -->
        <template for:each={displayRows} for:item="row">
          <tr key={row.id}>
            <td class="slds-text-align_center">{row.date}</td>
            <td class="slds-text-align_center">{row.begBalance}</td>
            <td class="slds-text-align_center">{row.accrual}</td>
            <td class="slds-text-align_center">{row.payment}</td>
            <td class="slds-text-align_center">{row.outstandingInt}</td>
          </tr>
        </template>
      </tbody>
    </table>
  </div>
</template>