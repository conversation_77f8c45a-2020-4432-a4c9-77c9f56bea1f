/* eslint-disable */
// // import { LightningElement,api } from 'lwc';
// // export default class InterestAccrual extends LightningElement {
// //      @api weekColumns = [];
// //    @api transactions = [];
// //   @api disbursements = [];

// // }


// import { LightningElement, api } from 'lwc';

// export default class InterestAccrual extends LightningElement {
//   /** raw lists passed in from parent */
//   @api transactions = [];
//   @api disbursements = [];

//   /** helper to grab the single disbursement’s project ID */
//   get projectId() {
//     return Array.isArray(this.disbursements) && this.disbursements.length
//       ? this.disbursements[0].Project__c
//       : null;
//   }

//   /** build the rows for the table with interest accrual logic */
//   get displayRows() {
//     const txns = (Array.isArray(this.transactions) ? this.transactions : [])
//       .filter(tx => tx.Project__c === this.projectId)
//       .sort((a, b) => new Date(a.CreatedDate) - new Date(b.CreatedDate));

//     let prevOutstanding = 0;

//     return txns.map(tx => {
//       const begBalance = prevOutstanding;
//       const accrual = tx.Accrued_Interests_to_Date__c || 0;
//       const payment = tx.Interest_Application__c || 0;
//       const outstandingInt = begBalance + accrual - payment;

//       const row = {
//         id: tx.Id,
//         date: new Date(tx.CreatedDate).toLocaleDateString(),
//         begBalance: `$ ${begBalance.toFixed(2)}`,
//         accrual: `$ ${accrual.toFixed(2)}`,
//         payment: `$ ${payment.toFixed(2)}`,
//         outstandingInt: `$ ${outstandingInt.toFixed(2)}`
//       };

//       prevOutstanding = outstandingInt;
//       return row;
//     });
//   }
// }


import { LightningElement, api } from "lwc";

export default class InterestAccrual extends LightningElement {
  @api transactions = [];
  @api disbursements = [];

  /** Grab the one and only Project__c from disbursements (if exists) */
  get projectId() {
    return Array.isArray(this.disbursements) && this.disbursements.length
      ? this.disbursements[0].Project__c
      : null;
  }

  /**
   * Given a JS Date, return the Date object for the Friday of that week.
   * If already Friday (getDay() === 5), return it (with time zeroed).
   * Otherwise, move forward to the next Friday.
   */
  getNextFriday(date) {
    const d = new Date(date);
    const dow = d.getDay(); // 0=Sun,1=Mon,…5=Fri,6=Sat
    const offset = (5 - dow + 7) % 7;
    d.setDate(d.getDate() + offset);
    d.setHours(0, 0, 0, 0);
    return d;
  }

  /**
   * Build one “augmented” object per transaction:
   *   - Calculate accrual, payment, and outstandingInterest = (begBalance + accrual - payment)
   *   - Assign each transaction to a “weekKey” = next Friday of CreatedDate
   * Then group those by weekKey, summing accrual, payment, and outstandingInterest.
   * Finally build rows where begBalance = previous week’s outstanding, week = 1,2,3…
   */
  get displayRows() {
    // 1) Filter + sort transactions for this project
    const txns = (Array.isArray(this.transactions) ? this.transactions : [])
      .filter((tx) => tx.Project__c === this.projectId)
      .sort((a, b) => {
        return new Date(a.CreatedDate).getTime() - new Date(b.CreatedDate).getTime();
      });

    // 2) Build per-txn array with fields and weekKey
    let runningOutstanding = 0; // this is per-transaction “prevOutstanding”, though we won't sum these directly
    const perTxn = txns.map((tx) => {
      const txDate = new Date(tx.CreatedDate);

      // a) accrual and payment from your fields
      const accrual = tx.Accrued_Interests_to_Date__c || 0;
      const payment = tx.Interest_Application__c || 0;

      // b) outstanding interest for this single transaction
      //    (we still track runningOutstanding so that “single-txn” begBalance flows to the next transaction,
      //     but that doesn’t directly feed the final week’s begBalance because we re-group by Friday.)
      const outstandingInterest = runningOutstanding + accrual - payment;

      // c) compute weekKey = the Friday for this CreatedDate
      const weekKey = this.getNextFriday(txDate);

      // d) update runningOutstanding for next iteration
      runningOutstanding = outstandingInterest;

      return {
        weekKey,
        accrual,
        payment,
        outstandingInterest
      };
    });

    // 3) Group perTxn entries by weekKey timestamp, summing accrual, payment, outstandingInterest
    const groups = perTxn.reduce((acc, obj) => {
      const key = obj.weekKey.getTime();
      if (!acc[key]) {
        acc[key] = {
          weekDate: obj.weekKey,
          accrualSum: 0,
          paymentSum: 0,
          outstandingSum: 0
        };
      }
      acc[key].accrualSum += obj.accrual;
      acc[key].paymentSum += obj.payment;
      acc[key].outstandingSum += obj.outstandingInterest;
      return acc;
    }, {});

    // 4) Convert groups into a sorted array by weekDate
    const sortedFridays = Object.values(groups).sort((a, b) => {
      return a.weekDate.getTime() - b.weekDate.getTime();
    });

    // 5) Build final rows. Week 1 begBalance = 0; subsequent week begBalance = prev outstandingSum
    let weekCounter = 1;
    let prevWeekOutstanding = 0;
    return sortedFridays.map((grp) => {
      // a) week’s begBalance
      const begBalance = weekCounter === 1 ? 0 : prevWeekOutstanding;
      // b) this week’s outstanding (already summed)
      const outstandingThisWeek = grp.outstandingSum;

      const row = {
        id: grp.weekDate.getTime().toString(),
        date: grp.weekDate.toLocaleDateString(),
        week: weekCounter,
        begBalance: `$ ${begBalance.toFixed(2)}`,
        accrual: `$ ${grp.accrualSum.toFixed(2)}`,
        payment: `$ ${grp.paymentSum.toFixed(2)}`,
        outstandingInt: `$ ${outstandingThisWeek.toFixed(2)}`
      };

      prevWeekOutstanding = outstandingThisWeek;
      weekCounter++;
      return row;
    });
  }
}