<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Process_Flinks_Response</name>
        <label>Process Flinks Response</label>
        <locationX>308</locationX>
        <locationY>276</locationY>
        <actionName>FlinksAsyncCallout</actionName>
        <actionType>apex</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>requestId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>responseBody</name>
            <value>
                <elementReference>$Record.Response_Json__c</elementReference>
            </value>
        </inputParameters>
        <nameSegment>FlinksAsyncCallout</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>63.0</apiVersion>
    <environments>Default</environments>
    <interviewLabel>Flinks Log Fallback Processing {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Flinks Log Fallback Processing</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Flinks_Function__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>GetAccountsDetail</stringValue>
            </value>
        </filters>
        <filters>
            <field>Response_Json__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>Flinks_Logs__c</object>
        <recordTriggerType>Create</recordTriggerType>
        <scheduledPaths>
            <name>X5_Mins_After_record_is_created</name>
            <connector>
                <targetReference>Process_Flinks_Response</targetReference>
            </connector>
            <label>5 Mins After record is created</label>
            <maxBatchSize>1</maxBatchSize>
            <offsetNumber>1</offsetNumber>
            <offsetUnit>Minutes</offsetUnit>
            <timeSource>RecordTriggerEvent</timeSource>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
