<?xml version="1.0" encoding="UTF-8"?>
<Report xmlns="http://soap.sforce.com/2006/04/metadata">
    <columns>
        <field>CHILD_NAME</field>
    </columns>
    <columns>
        <field>Bank_Transaction__c.Balance__c</field>
    </columns>
    <columns>
        <field>Bank_Transaction__c.Description__c</field>
    </columns>
    <columns>
        <field>Bank_Transaction__c.AI_Reasoning__c</field>
    </columns>
    <columns>
        <field>Bank_Transaction__c.AI_Category__c</field>
    </columns>
    <columns>
        <field>Bank_Transaction__c.Transaction_Date__c</field>
    </columns>
    <columns>
        <aggregateTypes>Sum</aggregateTypes>
        <field>Bank_Transaction__c.Debit__c</field>
    </columns>
    <columns>
        <aggregateTypes>Sum</aggregateTypes>
        <field>Bank_Transaction__c.Credit__c</field>
    </columns>
    <filter>
        <criteriaItems>
            <column>Bank_Transaction__c.Transaction_Date__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>greaterOrEqual</operator>
            <value>1/31/2025</value>
        </criteriaItems>
        <criteriaItems>
            <column>Bank_Transaction__c.Transaction_Date__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>lessThan</operator>
            <value>2/28/2025</value>
        </criteriaItems>
        <criteriaItems>
            <column>CUST_ID</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>a3LUN00000MHSOG</value>
        </criteriaItems>
        <language>en_US</language>
    </filter>
    <format>Summary</format>
    <groupingsDown>
        <dateGranularity>Day</dateGranularity>
        <field>Bank_Transaction__c.Transaction_Type__c</field>
        <sortOrder>Asc</sortOrder>
    </groupingsDown>
    <name>Transactions by month</name>
    <params>
        <name>co</name>
        <value>1</value>
    </params>
    <reportType>CustomEntityCustomEntity$Bank_Account__c$Bank_Transaction__c</reportType>
    <scope>organization</scope>
    <showDetails>true</showDetails>
    <showGrandTotal>true</showGrandTotal>
    <showSubTotals>true</showSubTotals>
    <timeFrameFilter>
        <dateColumn>CUST_CREATED_DATE</dateColumn>
        <interval>INTERVAL_CUSTOM</interval>
    </timeFrameFilter>
</Report>
