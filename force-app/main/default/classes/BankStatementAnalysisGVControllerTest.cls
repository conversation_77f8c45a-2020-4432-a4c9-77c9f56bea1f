@IsTest
private class BankStatementAnalysisGVControllerTest {

    @TestSetup
    static void setupTestData() {
        Bank_Account__c bankAccount = new Bank_Account__c(Name = 'Test Bank Account');
        insert bankAccount;
        
        List<Bank_Transaction__c> transactions = new List<Bank_Transaction__c>();
        
        transactions.add(new Bank_Transaction__c(
            Bank_Account__c = bankAccount.Id,
            Transaction_Id__c = 'TID-Old',
            Balance__c = 500.00,
            Debit__c = 100.00,
            Description__c = 'Old Transaction',
            Category__c = 'Old Category',
            Sub_Category__c = 'Old Sub Category',
            Transaction_Date__c = Date.today().addMonths(-8)
        ));
        
        for (Integer i = 0; i < 5; i++) {
            Bank_Transaction__c trans = new Bank_Transaction__c(
                Bank_Account__c = bankAccount.Id,
                Transaction_Id__c = 'TID-' + i,
                Balance__c = 1000.00 + i * 100,
                Description__c = 'Test Description ' + i,
                Category__c = 'Category ' + i,
                Sub_Category__c = 'Sub Category ' + i,
                Transaction_Date__c = Date.today().addMonths(-i)
            );
            if (Math.mod(i, 2) == 0) {
                trans.Debit__c = 200.00 + i * 10;
            } else {
                trans.Credit__c = 150.00 + i * 20;
            }
            transactions.add(trans);
        }
        insert transactions;
    }
    
    @IsTest
    static void testGetStatementDataMFCategory() {
        Bank_Account__c bankAccount = [SELECT Id FROM Bank_Account__c LIMIT 1];
        
        Test.startTest();
        BankStatementAnalysisGVController.StatementData result = BankStatementAnalysisGVController.getStatementData(
            bankAccount.Id, '6', 'MF_Category__c'
        );
        Test.stopTest();
        
        System.assertNotEquals(null, result);
        System.assertNotEquals(null, result.transactions);
        System.assert(result.transactions.size() > 0, 'Transactions should be present');
    }
    
    @IsTest
    static void testGetStatementDataAICategory() {
        Bank_Account__c bankAccount = [SELECT Id FROM Bank_Account__c LIMIT 1];
        
        Test.startTest();
        BankStatementAnalysisGVController.StatementData result = BankStatementAnalysisGVController.getStatementData(
            bankAccount.Id, '6', 'AI_Category__c'
        );
        Test.stopTest();
        
        System.assertNotEquals(null, result);
        System.assertNotEquals(null, result.transactions);
    }
    
    @IsTest
    static void testGetStatementDataMFCategoryManualOverride() {
        Bank_Account__c bankAccount = [SELECT Id FROM Bank_Account__c LIMIT 1];
        
        Test.startTest();
        BankStatementAnalysisGVController.StatementData result = BankStatementAnalysisGVController.getStatementData(
            bankAccount.Id, '6', 'MF_Category_Manual_Override__c'
        );
        Test.stopTest();
        
        System.assertNotEquals(null, result);
        System.assertNotEquals(null, result.transactions);
    }
    
    @IsTest
    static void testGetStatementDataInvalidInput() {
        Test.startTest();
        try {
            BankStatementAnalysisGVController.getStatementData(null, null, null);
            System.assert(false, 'Should throw an exception');
        } catch (AuraHandledException e) {
            System.debug('Caught expected exception: ' + e.getMessage());
        }
        Test.stopTest();
    }
}