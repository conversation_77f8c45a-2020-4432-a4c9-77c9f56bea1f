/**
 * @description This class is responsible for retrieving all data related to cashflow for a specific project.
 * It serves as a data service for the Cashflow Tracker UI, ensuring Field Level Security (FLS)
 * and sharing rules are respected. It dynamically fetches fields for Project__c, Cashflow__c, Cashflow_Line_Item__c,
 * and its child object Cashflow_Line_Item_Child__c.
 * <AUTHOR>
 * @date 12 May 2025 (updated 27 May 2025 for child object integration)
 */
@SuppressWarnings('PMD.AvoidDeeplyNestedIfStmts, PMD.CognitiveComplexity, PMD.ExcessiveMethodLength,PMD')
public with sharing class CashflowDataService {

    private static final String CLASS_NAME = 'CashflowDataService';
    private static final String CHILD_LINE_ITEM_OBJECT_API_NAME = 'Cashflow_Line_Item_Child__c';
    private static final String CHILD_LINE_ITEM_RELATIONSHIP_NAME = 'Cashflow_Line_Item_Child__r';

    /**
     * @description Retrieves all accessible fields for a given SObject type.
     * @param sObjectApiName The API name of the SObject.
     * @return List<String> A list of API names of accessible fields.
     */
    private static List<String> getAllAccessibleFields(String sObjectApiName) {
        final String METHOD_NAME = 'getAllAccessibleFields';
        DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, new Map<String, Object>{'sObjectApiName' => sObjectApiName});
        
        List<String> accessibleFields = new List<String>();
        if (String.isBlank(sObjectApiName)) {
            DebugLogUtil.warn(METHOD_NAME + ': SObject API name is blank.');
            return accessibleFields;
        }

        try {
            Map<String, Schema.SObjectType> globalDescribe = Schema.getGlobalDescribe();
            Schema.SObjectType sObjectType = globalDescribe.get(sObjectApiName.toLowerCase()); // Use toLowerCase for safety

            if (sObjectType == null) {
                DebugLogUtil.error(METHOD_NAME + ': SObject type not found for API name: ' + sObjectApiName);
                return accessibleFields;
            }

            Schema.DescribeSObjectResult sObjectDescribe = sObjectType.getDescribe();
            Map<String, Schema.SObjectField> fieldMap = sObjectDescribe.fields.getMap();

            for (Schema.SObjectField field : fieldMap.values()) {
                Schema.DescribeFieldResult fieldDescribe = field.getDescribe();
                if (fieldDescribe.isAccessible()) { // Check FLS for readability
                    accessibleFields.add(fieldDescribe.getName());
                }
            }
            DebugLogUtil.info(METHOD_NAME + ': Found ' + accessibleFields.size() + ' accessible fields for ' + sObjectApiName);
        } catch (Exception e) {
            DebugLogUtil.error(METHOD_NAME + ': Error getting fields for ' + sObjectApiName, e);
        }
        
        DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME);
        return accessibleFields;
    }

    /**
     * @description Retrieves all necessary data for the Cashflow Tracker UI for a given Project.
     * Applies FLS checks before returning data. Respects Sharing rules.
     * Fetches Project, Cashflow, parent Cashflow_Line_Item__c (with aggregate values),
     * and their child Cashflow_Line_Item_Child__c records.
     * @param projectId The Id of the Project__c record.
     * @param cashflowId The Id of the Cashflow__c record.
     * @return CashflowPageData Wrapper containing all permitted data for the UI.
     */
    @AuraEnabled(cacheable=true)
    public static CashflowPageData getCashflowData(Id projectId, Id cashflowId) {
        final String METHOD_NAME = 'getCashflowData';
        DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, new Map<String, Object>{'projectId' => projectId, 'cashflowId' => cashflowId});

        CashflowPageData pageData = new CashflowPageData();

        if (String.isBlank(projectId)) {
            DebugLogUtil.error(METHOD_NAME + ': Project ID is null or blank.');
            throw new AuraHandledException('Project ID is required.');
        }

        try {
            // --- 1. Get Project Header Data ---
            List<String> projectFields = getAllAccessibleFields('Project__c');
            if (projectFields.isEmpty()) {
                DebugLogUtil.error(METHOD_NAME + ': No accessible fields found for Project__c. Cannot query.');
                throw new AuraHandledException('No accessible fields found for Project object.');
            }
            if(!projectFields.contains('Id')) {projectFields.add('Id');}

            String projectQuery = 'SELECT ' + String.join(projectFields, ',') +
                                  ' FROM Project__c WHERE Id = :projectId LIMIT 1';
            DebugLogUtil.info(METHOD_NAME + ': Dynamic Project Query: ' + projectQuery);
            // ... (rest of project data fetching remains the same) ...
             List<SObject> tempProjectList = Database.query(projectQuery);
            if (!tempProjectList.isEmpty()) {
                SObjectAccessDecision projectDecision = Security.stripInaccessible(AccessType.READABLE, tempProjectList);
                if(!projectDecision.getRecords().isEmpty()){
                    pageData.project = (Project__c)projectDecision.getRecords()[0];
                    DebugLogUtil.info(METHOD_NAME + ': Project data fetched and FLS checked for Project ID: ' + projectId);
                } else { // ... (error handling) ...
                    DebugLogUtil.warn(METHOD_NAME + ': Project found but no fields are accessible after FLS check for Project ID: ' + projectId);
                    throw new AuraHandledException('You do not have access to view the details of this project.');
                }
            } else { // ... (error handling) ...
                 DebugLogUtil.warn(METHOD_NAME + ': Project not found for ID: ' + projectId);
                 throw new AuraHandledException('Project not found with ID: ' + projectId);
            }


            // --- 2. Get Active Cashflow Version and its Line Items (Parent and Children) ---
            Id activeCashflowIdToQuery;
            if(cashflowId != null) {
                activeCashflowIdToQuery = cashflowId;
            } else {
                activeCashflowIdToQuery = getActiveCashflowId(projectId);
            } 

            if (activeCashflowIdToQuery != null) {
                DebugLogUtil.info(METHOD_NAME + ': Effective Cashflow ID for query: ' + activeCashflowIdToQuery + ' for Project ID: ' + projectId);
                
                List<String> cashflowFields = getAllAccessibleFields('Cashflow__c');
                if (cashflowFields.isEmpty()) { // ... (error handling) ...
                    DebugLogUtil.error(METHOD_NAME + ': No accessible fields found for Cashflow__c. Cannot query active cashflow.');
                    throw new AuraHandledException('No accessible fields found for Cashflow object.');
                }
                if(!cashflowFields.contains('Id')) {cashflowFields.add('Id');}
                // ... (active cashflow fetching remains similar) ...
                String activeCashflowQuery = 'SELECT ' + String.join(cashflowFields, ',') +
                                             ' FROM Cashflow__c WHERE Id = :activeCashflowIdToQuery LIMIT 1';
                DebugLogUtil.info(METHOD_NAME + ': Dynamic Active Cashflow Query: ' + activeCashflowQuery);
                List<SObject> tempActiveCashflowList = Database.query(activeCashflowQuery);

                if(!tempActiveCashflowList.isEmpty()){
                    SObjectAccessDecision activeCashflowDecision = Security.stripInaccessible(AccessType.READABLE, tempActiveCashflowList);
                     if(!activeCashflowDecision.getRecords().isEmpty()){
                        pageData.activeCashflow = (Cashflow__c)activeCashflowDecision.getRecords()[0];
                        DebugLogUtil.info(METHOD_NAME + ': Active Cashflow record data fetched and FLS checked.');
                    } else { // ... (warning) ...
                         DebugLogUtil.warn(METHOD_NAME + ': Active Cashflow record found but no fields accessible after FLS for ID: ' + activeCashflowIdToQuery);
                    }
                } else { // ... (warning) ...
                    DebugLogUtil.warn(METHOD_NAME + ': Active Cashflow record not found for ID: ' + activeCashflowIdToQuery);
                }

                // Fetch Parent Cashflow_Line_Item__c and their Children (Cashflow_Line_Item_Child__c)
                List<String> forecastParentFields = getAllAccessibleFields('Cashflow_Line_Item__c');
                if (forecastParentFields.isEmpty()) {
                     DebugLogUtil.warn(METHOD_NAME + ': No accessible fields found for Cashflow_Line_Item__c. Forecast lines will be empty.');
                     pageData.forecastLines = new List<Cashflow_Line_Item__c>();
                } else {
                    if(!forecastParentFields.contains('Id')) {forecastParentFields.add('Id');}
                    if(!forecastParentFields.contains('Cashflow__c')) {forecastParentFields.add('Cashflow__c');}
                    if(!forecastParentFields.contains('Week_Start_Date__c')) {forecastParentFields.add('Week_Start_Date__c');}
                    if(!forecastParentFields.contains('Line_Item_Category__c')) {forecastParentFields.add('Line_Item_Category__c');}
                    if(!forecastParentFields.contains('Planned_Amount__c')) {forecastParentFields.add('Planned_Amount__c');} // Crucial aggregate field
                    // Add other parent CLI fields that might be used as templates for children if needed

                    String childSubQuery = '';
                    if (Schema.getGlobalDescribe().containsKey(CHILD_LINE_ITEM_OBJECT_API_NAME.toLowerCase())) {
                        List<String> childDetailAccessibleFields = getAllAccessibleFields(CHILD_LINE_ITEM_OBJECT_API_NAME);
                        List<String> childFieldsToQuery = new List<String>{'Id'}; // Always include Id
                            if(childDetailAccessibleFields.contains('Amount__c')) {childFieldsToQuery.add('Amount__c');}
                        if(childDetailAccessibleFields.contains('Description__c')) {childFieldsToQuery.add('Description__c');}
                        // Add other relevant child fields based on accessibility and popover needs
                        // e.g., Sub_Amount_Variation__c, Sub_Payment_Frequency__c if they moved to child
                        
                        if (childFieldsToQuery.size() > 0) {
                             // Ensure the relationship name is correct
                            childSubQuery = ', (SELECT ' + String.join(childFieldsToQuery, ',') + 
                                            ' FROM ' + CHILD_LINE_ITEM_RELATIONSHIP_NAME + ')';
                        }
                    } else {
                        DebugLogUtil.warn(METHOD_NAME + ': Child object ' + CHILD_LINE_ITEM_OBJECT_API_NAME + ' not found in schema. Cannot query child line items.');
                    }

                    String forecastQuery = 'SELECT ' + String.join(forecastParentFields, ',') + childSubQuery +
                                          ' FROM Cashflow_Line_Item__c WHERE Cashflow__c = :activeCashflowIdToQuery ' +
                                          ' ORDER BY Week_Start_Date__c NULLS FIRST, Line_Item_Category__c';
                    DebugLogUtil.info(METHOD_NAME + ': Dynamic Forecast Lines Query (with children): ' + forecastQuery);

                    List<SObject> tempForecastLines = Database.query(forecastQuery);
                    SObjectAccessDecision forecastDecision = Security.stripInaccessible(AccessType.READABLE, tempForecastLines);
                    pageData.forecastLines = (List<Cashflow_Line_Item__c>)forecastDecision.getRecords();
                    DebugLogUtil.info(METHOD_NAME + ': ' + pageData.forecastLines.size() + ' parent forecast lines (with children) fetched and FLS checked for Active Cashflow ID: ' + activeCashflowIdToQuery);
                }

            } else {
                DebugLogUtil.warn(METHOD_NAME + ': No active/specified Cashflow version found for Project ID: ' + projectId);
                pageData.forecastLines = new List<Cashflow_Line_Item__c>(); 
            }

            // --- 3. Get Actual Transactions (Payments, Disbursements, Adjustments) ---
            // This section remains the same unless Transaction__c structure also changed.
            List<String> transactionFields = getAllAccessibleFields('Transaction__c');
            if (transactionFields.isEmpty()) {
                 DebugLogUtil.warn(METHOD_NAME + ': No accessible fields for Transaction__c, transactions will be empty.');
                 pageData.transactions = new List<Transaction__c>();
            } else {
                if(!transactionFields.contains('Id')) {transactionFields.add('Id');}
                if(!transactionFields.contains('Project__c')) {transactionFields.add('Project__c');}
                 // Add other essential fields as needed, e.g. 'Transaction_Date__c', 'Amount__c'
                if(!transactionFields.contains('Transaction_Date__c')){ transactionFields.add('Transaction_Date__c');}


                String transactionQuery = 'SELECT ' + String.join(transactionFields, ',') +
                                        ' FROM Transaction__c WHERE Project__c = :projectId ' + 
                                        ' ORDER BY Transaction_Date__c'; 
                DebugLogUtil.info(METHOD_NAME + ': Transaction Query: ' + transactionQuery);

                List<SObject> tempTransactions = Database.query(transactionQuery);
                SObjectAccessDecision transactionDecision = Security.stripInaccessible(AccessType.READABLE, tempTransactions);
                pageData.transactions = (List<Transaction__c>)transactionDecision.getRecords();
                DebugLogUtil.info(METHOD_NAME + ': ' + pageData.transactions.size() + ' transactions fetched and FLS checked for Project ID: ' + projectId);
            }

            // --- 4. Get Disbursements ---
            List<String> disbursementFields = getAllAccessibleFields('Disbursement__c');
            if (disbursementFields.isEmpty()) {
                DebugLogUtil.warn(METHOD_NAME + ': No accessible fields for Disbursement__c, disbursements will be empty.');
                pageData.disbursements = new List<Disbursement__c>();
            } else {
                if (!disbursementFields.contains('Id')) disbursementFields.add('Id');
                if (!disbursementFields.contains('Project__c')) disbursementFields.add('Project__c');
                
                String disbursementQuery = 'SELECT ' + String.join(disbursementFields, ',') +
                                            ' FROM Disbursement__c WHERE Project__c = :projectId ' +
                                            ' ORDER BY CreatedDate DESC';
                DebugLogUtil.info(METHOD_NAME + ': Disbursement Query: ' + disbursementQuery);

                List<SObject> tempDisbursements = Database.query(disbursementQuery);
                SObjectAccessDecision disbursementDecision = Security.stripInaccessible(AccessType.READABLE, tempDisbursements);
                pageData.disbursements = (List<Disbursement__c>)disbursementDecision.getRecords();
                DebugLogUtil.info(METHOD_NAME + ': ' + pageData.disbursements.size() + ' disbursements fetched and FLS checked.');
            }

            // --- 5. Get Pay Applications ---

            List<String> payAppFields = getAllAccessibleFields('Pay_Application__c');
            if (payAppFields.isEmpty()) {
                DebugLogUtil.warn(METHOD_NAME + ': No accessible fields for Pay_Application__c, payApplications will be empty.');
                pageData.payApplications = new List<Pay_Application__c>();
            } else {
                if (!payAppFields.contains('Id')) payAppFields.add('Id');
                if (!payAppFields.contains('Project__c')) payAppFields.add('Project__c');

                String payAppQuery = 'SELECT ' + String.join(payAppFields, ',') +
                                    ' FROM Pay_Application__c WHERE Project__c = :projectId ' +
                                    ' ORDER BY CreatedDate DESC';
                DebugLogUtil.info(METHOD_NAME + ': Pay Application Query: ' + payAppQuery);

                List<SObject> tempPayApps = Database.query(payAppQuery);
                SObjectAccessDecision payAppDecision = Security.stripInaccessible(AccessType.READABLE, tempPayApps);
                pageData.payApplications = (List<Pay_Application__c>)payAppDecision.getRecords();
                DebugLogUtil.info(METHOD_NAME + ': ' + pageData.payApplications.size() + ' pay applications fetched and FLS checked.');
            }


        } catch (Exception e) {
            DebugLogUtil.error(METHOD_NAME + ': Error fetching cashflow data for Project ID: ' + projectId + '. Error: ' + e.getMessage() + '. Stacktrace: ' + e.getStackTraceString());
            // Rethrow to allow AuraHandledException to be caught by LWC if it's of that type
            if (e instanceof AuraHandledException) {throw e;}
            throw new AuraHandledException('An error occurred while loading cashflow data: ' + e.getMessage());
        }

        DebugLogUtil.info(METHOD_NAME + ': Cashflow data retrieval complete for Project ID: ' + projectId);
        DebugLogUtil.saveLogs();
        return pageData;
    }

    /**
     * @description Helper method to find the Id of the currently active Cashflow version for a project.
     * @param projectId The Id of the Project__c record.
     * @return Id The Id of the active Cashflow__c record, or null if none found.
    */
    @testVisible
    private static Id getActiveCashflowId(Id projectId) {
        // ... (this method remains the same) ...
        final String METHOD_NAME = 'getActiveCashflowId';
        DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, new Map<String, Object>{'projectId' => projectId});
        
        List<Cashflow__c> activeFlows = new List<Cashflow__c>();
        try {
            // Ensure FLS for Is_Active__c, Project__c, CreatedDate if not using Security.stripInaccessible here
            activeFlows = [SELECT Id,Projected_Weeks_Outstanding__c FROM Cashflow__c
                           WHERE Project__c = :projectId AND Is_Active__c = true
                           ORDER BY CreatedDate DESC LIMIT 1];
        } catch (Exception e) {
            DebugLogUtil.error(METHOD_NAME + ': Error querying active cashflow for Project ID: ' + projectId, e);
            return null;
        }

        Id activeFlowId = null;
        if (!activeFlows.isEmpty()) {
            activeFlowId = activeFlows[0].Id;
            DebugLogUtil.info(METHOD_NAME + ': Active Cashflow ID found: ' + activeFlowId + ' for Project ID: ' + projectId);
        } else {
            DebugLogUtil.info(METHOD_NAME + ': No active Cashflow found for Project ID: ' + projectId);
        }
        DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME);
        return activeFlowId;
    }

    /**
    * @description Wrapper class to structure the data returned to the LWC.
    */
    public class CashflowPageData {
        @AuraEnabled public Project__c project { get; set; }
        @AuraEnabled public Cashflow__c activeCashflow { get; set; } 
        // forecastLines now contains parent Cashflow_Line_Item__c records,
        // each potentially containing a list of its children (Cashflow_Line_Item_Child__c)
        @AuraEnabled public List<Cashflow_Line_Item__c> forecastLines { get; set; }
        @AuraEnabled public List<Transaction__c> transactions { get; set; }
        @AuraEnabled public List<Disbursement__c> disbursements {get; set;}
        @AuraEnabled public List<Pay_Application__c> payApplications {get; set;}
        
        public CashflowPageData() {
            this.forecastLines = new List<Cashflow_Line_Item__c>();
            this.transactions = new List<Transaction__c>();
        }
    }

    
    // TODO: Implement or update your save method in a separate Apex class or here.
    // This method would be called by the LWC's handleGlobalSave.
    @AuraEnabled
    public static void saveCashflowDetails(
        List<Cashflow_Line_Item__c> parentItemsToUpsert,
        List<Id> parentItemIdsToDelete,
        List<Cashflow_Line_Item_Child__c> childItemsToUpsert, // Assuming this is the type for children
        List<Id> childItemIdsToDelete
    ) {
        // Respect FLS and Sharing for all DML operations.
        // Use Security.stripInaccessible before DML if needed, or perform checks.

        try {
            // Order of operations is important:
            // 1. Delete children
            if (childItemIdsToDelete != null && !childItemIdsToDelete.isEmpty()) {
                List<Cashflow_Line_Item_Child__c> childrenToDelete = new List<Cashflow_Line_Item_Child__c>();
                for(Id childId : childItemIdsToDelete){
                    childrenToDelete.add(new Cashflow_Line_Item_Child__c(Id=childId));
                }
                // Check FLS for delete on child object
                // Database.delete(childrenToDelete, false); // `false` for partial success
                delete as User childrenToDelete; 
            }

            // 2. Delete parents (if any)
            if (parentItemIdsToDelete != null && !parentItemIdsToDelete.isEmpty()) {
                List<Cashflow_Line_Item__c> parentsToDelete = new List<Cashflow_Line_Item__c>();
                for(Id parentId : parentItemIdsToDelete){
                    parentsToDelete.add(new Cashflow_Line_Item__c(Id=parentId));
                }
                // Check FLS for delete on parent object
                // Database.delete(parentsToDelete, false);
                delete as User parentsToDelete;
            }

            // 3. Upsert parents
            // For new parents, their Ids will be populated after this.
            // If children of new parents need to be linked, this requires careful handling.
            // One approach: assign a temporary client-side ID to new parents, pass it with children,
            // then reconcile in Apex after parent insert. Or, multiple calls from LWC.
            // For simplicity, this example assumes childItemsToUpsert for new parents might need post-processing
            // or that new parents don't have children in the same save transaction initially from LWC.
            if (parentItemsToUpsert != null && !parentItemsToUpsert.isEmpty()) {
                // Ensure FLS for all fields being upserted
                // Database.upsert(parentItemsToUpsert, false);
                upsert as User parentItemsToUpsert;
            }

            // 4. Upsert children
            // Ensure Cashflow_Line_Item__c (parent lookup) is correctly populated on children.
            if (childItemsToUpsert != null && !childItemsToUpsert.isEmpty()) {
                // Ensure FLS for all fields being upserted
                // Database.upsert(childItemsToUpsert, false);
                upsert as User childItemsToUpsert;
            }

        } catch (Exception e) {
            DebugLogUtil.error('saveCashflowDetails: Error saving cashflow details', e);
            //throw new AuraHandledException('Error saving data: ' + e.getMessage());
        }
    }
    
}