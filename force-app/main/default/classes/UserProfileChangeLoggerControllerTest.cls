@isTest
private class UserProfileChangeLoggerControllerTest {

    // Helper method to create test users
    private static List<User> createTestUsers(Integer count, Id profileId, String baseAlias) {
        List<User> users = new List<User>();
        for (Integer i = 0; i < count; i++) {
            String uniqueAlias = baseAlias + i;
            // Ensure username is unique and follows email format for tests
            String uniqueUsername = uniqueAlias + '@testorg.com' + System.currentTimeMillis();
            User u = new User(
                Alias = uniqueAlias.left(8), // Max 8 chars for alias
                Email = uniqueAlias + '@testorg.com',
                EmailEncodingKey = 'UTF-8',
                LastName = 'TestUser' + i,
                LanguageLocaleKey = 'en_US',
                LocaleSidKey = 'en_US',
                ProfileId = profileId,
                TimeZoneSidKey = 'America/Los_Angeles',
                UserName = uniqueUsername
            );
            users.add(u);
        }
        return users;
    }

    @isTest
    static void testLogProfileChanges_SingleUser() {
        // Setup: Get two different profiles
        List<Profile> profiles = [SELECT Id, Name FROM Profile WHERE Name != null ORDER BY Name LIMIT 2];
        // Ensure we have at least two profiles to test a change
        System.assert(profiles.size() >= 2, 'Need at least two distinct profiles in the org to run this test.');
        Id profileAId = profiles[0].Id;
        String profileAName = profiles[0].Name;
        Id profileBId = profiles[1].Id;
        String profileBName = profiles[1].Name;

        // Create a test user with Profile A
        User testUser = createTestUsers(1, profileAId, 'chgusr')[0];
        insert testUser;

        // Prepare input lists
        List<Id> userIds = new List<Id>{ testUser.Id };
        List<Id> oldProfileIds = new List<Id>{ profileAId };
        List<Id> newProfileIds = new List<Id>{ profileBId };

        Test.startTest();
        UserProfileChangeLoggerController.logProfileChanges(userIds, oldProfileIds, newProfileIds);
        Test.stopTest(); // Ensure future method executes

        // Assertions: Verify the log record
        List<User_Profile_Change_Log__c> logs = [
            SELECT UserId__c, OldProfileName__c, NewProfileName__c, isActive__c, ChangeDate__c
            FROM User_Profile_Change_Log__c
            WHERE UserId__c = :testUser.Id
        ];

        System.assertEquals(1, logs.size(), 'Should create one log record.');
        User_Profile_Change_Log__c log = logs[0];
        System.assertEquals(testUser.Id, log.UserId__c, 'User ID mismatch.');
        //System.assertEquals(profileAName, log.OldProfileName__c, 'Old Profile Name mismatch.');
		System.assertEquals(profileAName.substring(0, Math.min(30, profileAName.length())), log.OldProfileName__c, 'Old Profile Name truncation mismatch.');
		System.assertEquals(profileBName.substring(0, Math.min(30, profileBName.length())), log.NewProfileName__c, 'New Profile Name truncation mismatch.');
        //System.assertEquals(profileBName, log.NewProfileName__c, 'New Profile Name mismatch.');
        System.assertEquals(true, log.isActive__c, 'isActive should be true for profile change.'); // User is still active
        System.assertNotEquals(null, log.ChangeDate__c, 'Change Date should be populated.');
    }

    @isTest
    static void testLogProfileChanges_BulkUsers() {
        // Setup: Get two profiles
        List<Profile> profiles = [SELECT Id, Name FROM Profile WHERE Name != null ORDER BY Name LIMIT 2];
        System.assert(profiles.size() >= 2, 'Need at least two distinct profiles in the org.');
        Id profileAId = profiles[0].Id;
        String profileAName = profiles[0].Name;
        Id profileBId = profiles[1].Id;
        String profileBName = profiles[1].Name;

        // Create multiple test users with Profile A
        List<User> testUsers = createTestUsers(3, profileAId, 'bulkchg');
        insert testUsers;

        // Prepare input lists
        List<Id> userIds = new List<Id>();
        List<Id> oldProfileIds = new List<Id>();
        List<Id> newProfileIds = new List<Id>();

        for (User u : testUsers) {
            userIds.add(u.Id);
            oldProfileIds.add(profileAId);
            newProfileIds.add(profileBId); // Change all to profile B
        }

        Test.startTest();
        UserProfileChangeLoggerController.logProfileChanges(userIds, oldProfileIds, newProfileIds);
        Test.stopTest(); // Ensure future method executes

        // Assertions: Verify the log records
        List<User_Profile_Change_Log__c> logs = [
            SELECT UserId__c, OldProfileName__c, NewProfileName__c, isActive__c
            FROM User_Profile_Change_Log__c
            WHERE UserId__c IN :userIds
            ORDER BY OldProfileName__c // Consistent order for assertion
        ];

        System.assertEquals(testUsers.size(), logs.size(), 'Should create one log record per user.');
        for (User_Profile_Change_Log__c log : logs) {
            System.assertEquals(profileAName.substring(0, Math.min(30, profileAName.length())), log.OldProfileName__c, 'Old Profile Name truncation mismatch.');
			System.assertEquals(profileBName.substring(0, Math.min(30, profileBName.length())), log.NewProfileName__c, 'New Profile Name truncation mismatch.');
            System.assertEquals(true, log.isActive__c, 'isActive should be true.');
        }
    }

     @isTest
    static void testLogProfileChanges_EmptyInput() {
        Test.startTest();
        UserProfileChangeLoggerController.logProfileChanges(new List<Id>(), new List<Id>(), new List<Id>());
        Test.stopTest();

        // Assertions: Verify no log records created
        List<User_Profile_Change_Log__c> logs = [SELECT Id FROM User_Profile_Change_Log__c];
        System.assertEquals(0, logs.size(), 'Should create zero log records for empty input.');
    }

     @isTest
    static void testLogUserDeactivations_SingleUser() {
         // Setup: Get a profile
        Profile testProfile = [SELECT Id, Name FROM Profile WHERE Name != null ORDER BY Name LIMIT 1];
        System.assert(testProfile != null, 'Need at least one profile in the org.');

        // Create a test user
        User testUser = createTestUsers(1, testProfile.Id, 'deactusr')[0];
        insert testUser;

        // Prepare input list
        List<Id> userIds = new List<Id>{ testUser.Id };

        Test.startTest();
        // Simulate deactivation by calling the logger
        UserProfileChangeLoggerController.logUserDeactivations(userIds);
        Test.stopTest(); // Ensure future method executes

        // Assertions: Verify the log record
        List<User_Profile_Change_Log__c> logs = [
            SELECT UserId__c, OldProfileName__c, NewProfileName__c, isActive__c, ChangeDate__c
            FROM User_Profile_Change_Log__c
            WHERE UserId__c = :testUser.Id
        ];

        System.assertEquals(1, logs.size(), 'Should create one log record.');
        User_Profile_Change_Log__c log = logs[0];
		//System.assertEquals(testProfile.Name.substring(0, Math.min(30, testProfile.Name.length())), log.OldProfileName__c, 'Old Profile Name truncation mismatch.');
        System.assertEquals(testUser.Id, log.UserId__c, 'User ID mismatch.');
        System.assertEquals(testProfile.Name.substring(0, Math.min(30, testProfile.Name.length())), log.OldProfileName__c, 'Old Profile Name truncation mismatch.');
        System.assertEquals(null, log.NewProfileName__c, 'New Profile Name should be null for deactivation.');
        System.assertEquals(false, log.isActive__c, 'isActive should be false for deactivation.');
        System.assertNotEquals(null, log.ChangeDate__c, 'Change Date should be populated.');
    }

      @isTest
    static void testLogUserDeactivations_BulkUsers() {
        // Setup: Get a profile
        Profile testProfile = [SELECT Id, Name FROM Profile WHERE Name != null ORDER BY Name LIMIT 1];
        System.assert(testProfile != null, 'Need at least one profile in the org.');

        // Create multiple test users
        List<User> testUsers = createTestUsers(4, testProfile.Id, 'bulkdeact');
        insert testUsers;

        // Prepare input list
        List<Id> userIds = new List<Id>();
        for(User u : testUsers) {
            userIds.add(u.Id);
        }

        Test.startTest();
        UserProfileChangeLoggerController.logUserDeactivations(userIds);
        Test.stopTest(); // Ensure future method executes

        // Assertions: Verify the log records
        List<User_Profile_Change_Log__c> logs = [
            SELECT UserId__c, OldProfileName__c, NewProfileName__c, isActive__c
            FROM User_Profile_Change_Log__c
            WHERE UserId__c IN :userIds
            ORDER BY OldProfileName__c
        ];

        System.assertEquals(testUsers.size(), logs.size(), 'Should create one log record per deactivated user.');
        for (User_Profile_Change_Log__c log : logs) {
            System.assertEquals(testProfile.Name.substring(0, Math.min(30, testProfile.Name.length())), log.OldProfileName__c, 'Old Profile Name truncation mismatch.');
            System.assertEquals(null, log.NewProfileName__c, 'New Profile Name should be null.');
            System.assertEquals(false, log.isActive__c, 'isActive should be false.');
        }
    }

    @isTest
    static void testLogUserDeactivations_EmptyInput() {
        Test.startTest();
        UserProfileChangeLoggerController.logUserDeactivations(new List<Id>());
        Test.stopTest();

        // Assertions: Verify no log records created
        List<User_Profile_Change_Log__c> logs = [SELECT Id FROM User_Profile_Change_Log__c];
        System.assertEquals(0, logs.size(), 'Should create zero log records for empty input.');
    }
}